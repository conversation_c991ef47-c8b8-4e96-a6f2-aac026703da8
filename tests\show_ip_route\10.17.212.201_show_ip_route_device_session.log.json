{"parsed_sample": [{"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:23:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:23:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:07:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:07:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:33:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:33:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:42:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:42:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:38:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:38:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,02:19:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,02:19:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:37:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:37:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:05:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:05:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:06:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:06:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:47:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:47:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:24:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:24:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:37:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:37:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:23:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:23:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,23:57:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,23:57:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,02:21:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,02:21:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:35:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:35:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,00:58:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,00:58:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:59:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:59:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:39:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:39:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:57:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:57:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:03:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:03:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,02:43:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,02:43:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,23:26:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,23:26:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,23:57:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,23:57:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:04:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:04:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:02:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:02:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:57:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:57:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:46:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:46:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:06:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:06:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:00:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:00:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:21:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:21:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:06:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:06:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,02:19:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,02:19:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:46:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:46:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:46:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:46:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:01:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:01:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:57:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:57:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:38:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:38:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:06:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:06:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,02:30:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,02:30:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,02:30:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,02:30:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:17:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:17:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:15:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:15:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:18:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:18:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:16:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:16:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:16:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:16:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "1d,05:17:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "1d,05:17:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:20:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:20:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:41:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:41:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:40:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:40:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:41:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:41:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:42:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:42:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:40:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:40:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:04:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:04:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:04:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:04:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,03:31:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,03:31:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,03:30:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,03:30:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:05:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:05:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:05:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:05:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,00:37:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,00:37:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:33:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:33:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "8d,21:39:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "8d,21:39:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:52:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "10d,21:42:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************6", "NEXTHOP_IF": "FHGigabitEthernet 0/32", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/31", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/30", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/29", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/28", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************6", "NEXTHOP_IF": "FHGigabitEthernet 0/27", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/26", "UPTIME": "10d,21:42:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "15d,19:23:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "15d,19:23:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/40", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/39", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "15d,19:23:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "15d,19:23:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/46", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/45", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/56", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/54", "UPTIME": "15d,19:23:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/36", "UPTIME": "15d,19:23:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/35", "UPTIME": "15d,19:23:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "14d,19:51:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "14d,19:51:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "14d,22:21:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "14d,22:21:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:09:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************4", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************4", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************8", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************8", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:08:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,21:05:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,23:08:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,23:08:49"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********0", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********4", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********8", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************8", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************4", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************8", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,05:27:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:57:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:06:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,19:14:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,19:14:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,19:14:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,19:14:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "00:03:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/56", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/54", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/46", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/45", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/40", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/39", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/36", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/35", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************6", "NEXTHOP_IF": "FHGigabitEthernet 0/32", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/31", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/30", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/29", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/28", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************6", "NEXTHOP_IF": "FHGigabitEthernet 0/27", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************2", "NEXTHOP_IF": "FHGigabitEthernet 0/26", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "10d,21:41:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "04:29:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "04:29:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,22:55:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,22:55:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:25:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:25:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:24:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:24:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "02:32:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "02:32:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:24:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:24:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:24:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:24:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:34:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:34:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:34:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:34:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "4d,04:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:41:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:41:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,20:21:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,20:21:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,01:24:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,01:24:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,20:11:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,20:11:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,03:57:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,03:57:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,20:11:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,20:11:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,01:25:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,01:25:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:13:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:13:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,01:03:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,01:03:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,01:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,01:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,22:41:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,22:41:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,20:21:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,20:21:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,01:03:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,01:03:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "7d,01:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "7d,01:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,03:32:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "1d,05:20:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "1d,05:20:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:20:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:20:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "1d,05:15:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "19:52:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "19:52:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "3d,01:21:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "3d,01:00:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "3d,01:00:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "1d,19:29:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "1d,05:22:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "2d,20:21:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "10d,21:40:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "7d,21:55:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "7d,21:55:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "7d,21:55:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "10d,20:18:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "8d,22:30:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "10d,21:28:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/16", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/15", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/14", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/13", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/12", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/11", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/10", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/9", "UPTIME": "8d,22:30:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "7d,01:37:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "7d,01:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "7d,01:37:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "7d,01:37:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "15d,19:23:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "15d,19:23:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "6d,22:42:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "6d,22:42:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:50:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "6d,22:44:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "6d,22:43:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "8d,23:03:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.13.128.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.13.128.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.0.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "15d,19:23:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.0.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "15d,19:23:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "8d,22:11:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "8d,22:11:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "9d,20:52:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "9d,20:52:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "5d,17:18:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "5d,17:18:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:52:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "8d,05:31:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "7d,20:32:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "9d,20:21:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "1d,23:42:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "1d,01:00:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "15d,01:18:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "15d,01:18:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "9d,22:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "2d,20:21:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "2d,20:21:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:24:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:24:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/40", "UPTIME": "23:31:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/39", "UPTIME": "23:31:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:22:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:22:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:22:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:22:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/40", "UPTIME": "23:38:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/39", "UPTIME": "23:38:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "2d,05:23:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "2d,05:23:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "11d,01:02:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "11d,01:02:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "2d,00:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "2d,00:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "1d,20:19:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "1d,20:19:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "1d,19:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "1d,19:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "2d,02:22:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "2d,02:22:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "9d,03:54:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "9d,03:54:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "9d,03:54:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "05:54:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "05:54:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "05:54:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "05:54:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:55:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "9d,21:05:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "2d,05:50:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "2d,05:50:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:29:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:29:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:29:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:29:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:29:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:29:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,04:29:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,04:29:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,19:14:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,19:14:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,01:16:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,01:16:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "3d,20:52:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "04:39:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "04:39:21"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "04:40:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "04:40:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/50", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/49", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/42", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/41", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/38", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/37", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/34", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/33", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/24", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/23", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/22", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/21", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/20", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/17", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/8", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/7", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/6", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/5", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/4", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "FHGigabitEthernet 0/3", "UPTIME": "10d,21:51:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/120", "UPTIME": "15d,19:23:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "FHGigabitEthernet 0/119", "UPTIME": "15d,19:23:29"}]}