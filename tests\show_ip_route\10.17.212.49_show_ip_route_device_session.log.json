{"parsed_sample": [{"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "0.0.0.0", "MASK": "0", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 7", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 7", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.2.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.2.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.2.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.2.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.9.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.9.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.9.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.9.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 10", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 10", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:07:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:07:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:07:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:07:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:33:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:33:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:33:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:33:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.12.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.12.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.12.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.12.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:29"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:37:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:37:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:37:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:37:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:05:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:05:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:05:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:05:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:02"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:47:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:47:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:47:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:47:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:24:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:24:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:24:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:24:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:37:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:37:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:37:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:37:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:23:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:21:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,02:21:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:21:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,02:21:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:35:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:35:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:35:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:35:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,00:58:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,00:58:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,00:58:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,00:58:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********8", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:59:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:59:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:59:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:59:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.13.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:39:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.13.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:39:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.13.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:39:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.13.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:39:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:26"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:03:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:43:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,02:43:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:43:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,02:43:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:26:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,23:26:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,23:26:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:26:54"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,23:57:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:04:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:04:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:04:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:04:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:02:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:12"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:00:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:21:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:21:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:21:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:21:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:06:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,02:19:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:58"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:46:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:01:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:57:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:38:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:06:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:06:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:06:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:06:32"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.14.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.14.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.14.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.14.0", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,02:30:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:57"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:18:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:18:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:18:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:18:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:16:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:17:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:20:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:20:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:20:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********7", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:20:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:08"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:42:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:40:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********28", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 17", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 17", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:52"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.31.32", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.31.32", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.31.32", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.31.32", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:04:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "27", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.44.0", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.44.0", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.44.0", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.44.0", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,03:31:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,03:31:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,03:31:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,03:31:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,03:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,03:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,03:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,03:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.120.0", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.120.0", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.120.0", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.120.0", "MASK": "21", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.128.0", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.128.0", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.128.0", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.128.0", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.200.31", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.200.31", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.200.31", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.200.31", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:05:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.212.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.212.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.212.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.212.9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,00:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,00:37:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********9", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,21:39:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,21:39:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,21:39:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "8d,21:39:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********01", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********01", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********01", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********01", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********02", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********03", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********04", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********05", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********06", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********07", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********09", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********13", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********14", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********18", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.215.128", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.215.128", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.216.96", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.216.96", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.216.96", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.216.96", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "HundredGigabitEthernet 1/0/49", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "HundredGigabitEthernet 2/0/49", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "HundredGigabitEthernet 1/0/50", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "HundredGigabitEthernet 2/0/50", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:09:17"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:08:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,21:05:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:08:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,23:08:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,23:08:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,23:08:53"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.228.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.228.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.228.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.228.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,05:27:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:57:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:27"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:05:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.249.6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.249.6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.249.6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.17.249.6", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:06:09"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:05:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:59"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "00:03:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "04:29:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "04:29:38"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 20", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.180.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.180.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.180.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.180.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:25:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:25:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:25:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:25:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "02:32:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "02:32:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "02:32:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "02:32:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 39", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 38", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.192.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.192.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.192.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.38.192.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "10.38.193.0", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 39", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 38", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "22", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 20", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "23", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.102.200.200", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.102.200.200", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.102.200.200", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.102.200.200", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 212", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 212", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:34:48"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 213", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 213", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "4d,04:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 237", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 237", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,01:24:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,01:24:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,01:24:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,01:24:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.236.214.5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,03:57:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.236.214.5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,03:57:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.236.214.5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,03:57:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "10.236.214.5", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,03:57:13"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:11:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,01:25:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,01:25:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,01:25:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,01:25:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:13:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,22:41:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:03:46"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "7d,01:13:42"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 236", "UPTIME": ""}, {"PROTOCOL": "C", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "", "METRIC": "", "NEXTHOP_IP": "", "NEXTHOP_IF": "VLAN 236", "UPTIME": ""}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:20:01"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:19:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:19:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "17.1.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:15:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:14:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "19:52:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.8", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:20:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.10", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:20:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.11", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "18.10.129.12", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:00:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,19:29:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,19:29:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,19:29:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,19:29:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,05:22:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,05:22:11"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:19"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:28"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,21:55:23"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,22:30:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,22:30:33"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,22:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,22:30:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:03"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:24"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,01:37:41"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "6d,22:41:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "6d,22:41:47"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.128.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.128.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.128.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "19.3.128.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "6d,22:45:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "6d,22:45:04"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "6d,22:43:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,23:03:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,23:03:44"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.0.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.0.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,22:12:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,22:12:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "5d,17:18:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "5d,17:18:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "8d,05:31:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "8d,05:31:49"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "7d,20:32:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,23:42:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,23:42:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,00:59:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,00:59:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.7.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,20:21:34"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:24:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:05"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "23:31:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "23:31:20"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:35"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:22:40"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:00"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "23:38:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "23:38:30"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:23:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:06"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,00:28:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.1", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,00:28:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,20:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.2", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,20:19:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "1d,19:49:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.3", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "1d,19:49:10"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,02:21:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "20.26.0.4", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,02:21:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "05:54:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "05:54:50"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "05:54:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "05:54:31"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "05:54:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "05:54:45"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "05:54:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "05:54:55"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.2.8.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.2.8.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.2.8.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.2.8.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:49:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:49:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:49:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:49:43"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:50:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "2d,05:50:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "2d,05:50:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "2d,05:50:25"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.13.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.13.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.13.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.13.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:37"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:36"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:49:38"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:16"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:15"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,04:29:14"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,19:14:56"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:16:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,01:16:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,01:16:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,01:16:39"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:51:51"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.23.192", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.23.192", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.23.192", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.17.23.192", "MASK": "26", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "3d,20:52:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.0.0", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "*********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "************", "MASK": "25", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.12.0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.12.0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.12.0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "43.83.12.0", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***************", "MASK": "30", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "04:39:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "04:39:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "04:39:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "04:39:22"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "04:40:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "04:40:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "04:40:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "**********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "04:40:18"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "32", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "*************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "***********", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}, {"PROTOCOL": "B", "TYPE": "", "NETWORK": "***********", "MASK": "24", "DISTANCE": "20", "METRIC": "0", "NEXTHOP_IP": "************", "NEXTHOP_IF": "", "UPTIME": "9d,03:33:07"}]}